#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TSP版本监控工具
24小时循环检测多个云端TSP文件版本更新
"""

import requests
import time
import logging
import json
from datetime import datetime
from typing import Dict, List, Tuple
import os
import sys


class TSPVersionMonitor:
    """TSP版本监控类"""
    
    def __init__(self, config_file: str = "config.json", mapping_file: str = "script_mapping.json", auto_download: bool = True):
        """
        初始化监控器

        Args:
            config_file: 配置文件路径
            mapping_file: 脚本映射文件路径
            auto_download: 是否自动下载新版本
        """
        self.config_file = config_file
        self.mapping_file = mapping_file
        self.auto_download = auto_download
        self.api_url = "http://app.touchsprite.com/api/device"
        self.download_url = "https://app.touchsprite.com/api/tsp-upgrade"
        self.headers = {
            "Content-Type": "application/x-www-form-urlencoded",
            "resource": "android"
        }
        self.fixed_params = {
            "uuid": "0198a5cf9a4c65cf4fc54ba4b58224be",
            "device_os": "android"
        }
        self.download_params = {
            "uuid": "0198a5cf9a4c65cf4fc54ba4b58224be"
        }

        # 设置日志
        self.setup_logging()

        # 加载配置和映射
        self.script_versions = self.load_config()
        self.script_mapping = self.load_mapping()

        self.logger.info("TSP版本监控器初始化完成")
    
    def setup_logging(self):
        """设置日志配置"""
        # 创建logs目录
        if not os.path.exists("logs"):
            os.makedirs("logs")
        
        # 配置日志格式
        log_format = "%(asctime)s - %(levelname)s - %(message)s"
        
        # 创建logger
        self.logger = logging.getLogger("TSPMonitor")
        self.logger.setLevel(logging.INFO)
        
        # 清除已有的处理器
        self.logger.handlers.clear()
        
        # 文件处理器
        file_handler = logging.FileHandler(
            f"logs/tsp_monitor_{datetime.now().strftime('%Y%m%d')}.log",
            encoding='utf-8'
        )
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(logging.Formatter(log_format))
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(logging.Formatter(log_format))
        
        # 添加处理器
        self.logger.addHandler(file_handler)
        self.logger.addHandler(console_handler)
    
    def load_config(self) -> Dict[str, str]:
        """
        加载配置文件
        
        Returns:
            script_id到版本号的映射字典
        """
        default_config = {
            "33085": "1.0.81",
            "34301": "1.0.17", 
            "34422": "1.0.19",
            "33586": "1.0.29",
            "33081": "1.0.224",
            "33086": "1.0.39",
            "33084": "1.0.45",
            "33609": "1.0.74"
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    self.logger.info(f"从配置文件加载了 {len(config)} 个脚本配置")
                    return config
            else:
                # 创建默认配置文件
                self.save_config(default_config)
                self.logger.info("创建了默认配置文件")
                return default_config
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return default_config
    
    def save_config(self, config: Dict[str, str]):
        """
        保存配置文件

        Args:
            config: 配置字典
        """
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            self.logger.info("配置文件保存成功")
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")

    def load_mapping(self) -> Dict[str, Dict]:
        """
        加载脚本映射文件

        Returns:
            script_id到映射信息的字典
        """
        try:
            if os.path.exists(self.mapping_file):
                with open(self.mapping_file, 'r', encoding='utf-8') as f:
                    mapping = json.load(f)
                    self.logger.info(f"从映射文件加载了 {len(mapping)} 个脚本映射")
                    return mapping
            else:
                self.logger.warning("未找到脚本映射文件，将使用script_id作为显示名称")
                return {}
        except Exception as e:
            self.logger.error(f"加载映射文件失败: {e}")
            return {}

    def get_script_display_name(self, script_id: str) -> str:
        """
        获取脚本的显示名称

        Args:
            script_id: 脚本ID

        Returns:
            脚本的显示名称
        """
        if script_id in self.script_mapping:
            return f"{self.script_mapping[script_id]['name']} ({script_id})"
        return script_id

    def get_script_folder(self, script_id: str) -> str:
        """
        获取脚本对应的文件夹名称

        Args:
            script_id: 脚本ID

        Returns:
            文件夹名称
        """
        if script_id in self.script_mapping:
            return self.script_mapping[script_id]['folder']
        return script_id

    def download_tsp_file(self, script_id: str, remote_version: str) -> bool:
        """
        下载新版本的TSP文件

        Args:
            script_id: 脚本ID
            remote_version: 远程版本号

        Returns:
            下载是否成功
        """
        script_display_name = self.get_script_display_name(script_id)
        folder_name = self.get_script_folder(script_id)

        try:
            # 构建下载请求参数
            download_data = {
                **self.download_params,
                "script_id": script_id
            }

            self.logger.info(f"🔄 开始下载 {script_display_name} 的新版本 {remote_version}...")

            # 发送下载请求
            response = requests.post(
                self.download_url,
                headers=self.headers,
                data=download_data,
                timeout=60  # 下载可能需要更长时间
            )

            if response.status_code == 200:
                # 确保脚本主文件夹存在
                if not os.path.exists(folder_name):
                    os.makedirs(folder_name)
                    self.logger.info(f"📁 创建脚本文件夹: {folder_name}")

                # 创建版本子文件夹
                version_folder = os.path.join(folder_name, remote_version)
                if not os.path.exists(version_folder):
                    os.makedirs(version_folder)
                    self.logger.info(f"📁 创建版本文件夹: {version_folder}")

                # 生成文件名
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"{script_id}_v{remote_version}_{timestamp}.tsp"
                filepath = os.path.join(version_folder, filename)

                # 保存文件
                with open(filepath, 'wb') as f:
                    f.write(response.content)

                file_size = len(response.content)
                self.logger.info(f"✅ 下载成功: {filepath} ({file_size} 字节)")

                # 更新本地版本号
                self.update_local_version(script_id, remote_version)

                return True
            else:
                self.logger.error(f"❌ 下载失败 - {script_display_name}: HTTP {response.status_code}")
                return False

        except requests.exceptions.Timeout:
            self.logger.error(f"❌ 下载超时 - {script_display_name}")
            return False
        except requests.exceptions.RequestException as e:
            self.logger.error(f"❌ 下载请求异常 - {script_display_name}: {e}")
            return False
        except Exception as e:
            self.logger.error(f"❌ 下载时发生未知错误 - {script_display_name}: {e}")
            return False

    def update_local_version(self, script_id: str, new_version: str):
        """
        更新本地版本号

        Args:
            script_id: 脚本ID
            new_version: 新版本号
        """
        try:
            # 更新内存中的版本号
            self.script_versions[script_id] = new_version

            # 更新配置文件
            self.save_config(self.script_versions)

            # 更新映射文件中的版本号
            if script_id in self.script_mapping:
                self.script_mapping[script_id]['version'] = new_version
                with open(self.mapping_file, 'w', encoding='utf-8') as f:
                    json.dump(self.script_mapping, f, indent=2, ensure_ascii=False)

            script_display_name = self.get_script_display_name(script_id)
            self.logger.info(f"📝 已更新 {script_display_name} 的本地版本号为: {new_version}")

        except Exception as e:
            self.logger.error(f"❌ 更新本地版本号失败: {e}")
    
    def compare_versions(self, local_version: str, remote_version: str) -> bool:
        """
        比较版本号
        
        Args:
            local_version: 本地版本号
            remote_version: 远程版本号
            
        Returns:
            True表示远程版本更高，False表示本地版本更高或相等
        """
        try:
            # 将版本号分割为数字列表进行比较
            local_parts = [int(x) for x in local_version.split('.')]
            remote_parts = [int(x) for x in remote_version.split('.')]
            
            # 补齐长度
            max_len = max(len(local_parts), len(remote_parts))
            local_parts.extend([0] * (max_len - len(local_parts)))
            remote_parts.extend([0] * (max_len - len(remote_parts)))
            
            # 逐位比较
            for local, remote in zip(local_parts, remote_parts):
                if remote > local:
                    return True
                elif remote < local:
                    return False
            
            return False  # 版本相等
        except Exception as e:
            self.logger.error(f"版本比较失败: {e}")
            return False
    
    def check_single_script(self, script_id: str, local_version: str) -> Tuple[bool, str]:
        """
        检查单个脚本的版本更新
        
        Args:
            script_id: 脚本ID
            local_version: 本地版本号
            
        Returns:
            (是否有更新, 远程版本号)
        """
        try:
            # 构建请求参数
            data = {
                **self.fixed_params,
                "script_id": script_id,
                "version": local_version
            }
            
            # 发送请求
            response = requests.post(
                self.api_url,
                headers=self.headers,
                data=data,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                
                # 根据API响应格式解析版本信息
                # 这里需要根据实际API响应格式调整
                if 'version' in result:
                    remote_version = result['version']
                elif 'data' in result and 'version' in result['data']:
                    remote_version = result['data']['version']
                else:
                    # 如果响应中没有版本信息，可能表示没有更新
                    self.logger.debug(f"脚本 {script_id} 响应中未找到版本信息")
                    return False, local_version
                
                # 比较版本
                has_update = self.compare_versions(local_version, remote_version)
                script_display_name = self.get_script_display_name(script_id)

                if has_update:
                    self.logger.warning(
                        f"🚨 脚本 {script_display_name} 发现更新: {local_version} -> {remote_version}"
                    )
                else:
                    self.logger.info(
                        f"✅ 脚本 {script_display_name} 版本正常: {local_version}"
                    )
                
                return has_update, remote_version
            else:
                script_display_name = self.get_script_display_name(script_id)
                self.logger.error(
                    f"请求失败 - 脚本 {script_display_name}: HTTP {response.status_code}"
                )
                return False, local_version

        except requests.exceptions.Timeout:
            script_display_name = self.get_script_display_name(script_id)
            self.logger.error(f"请求超时 - 脚本 {script_display_name}")
            return False, local_version
        except requests.exceptions.RequestException as e:
            script_display_name = self.get_script_display_name(script_id)
            self.logger.error(f"请求异常 - 脚本 {script_display_name}: {e}")
            return False, local_version
        except Exception as e:
            script_display_name = self.get_script_display_name(script_id)
            self.logger.error(f"检查脚本 {script_display_name} 时发生未知错误: {e}")
            return False, local_version
    
    def check_all_scripts(self) -> List[Dict]:
        """
        检查所有脚本的版本更新
        
        Returns:
            更新信息列表
        """
        updates = []
        self.logger.info(f"开始检查 {len(self.script_versions)} 个脚本的版本更新...")
        
        for script_id, local_version in self.script_versions.items():
            script_display_name = self.get_script_display_name(script_id)
            self.logger.info(f"检查脚本 {script_display_name} (当前版本: {local_version})")

            has_update, remote_version = self.check_single_script(script_id, local_version)

            if has_update:
                download_success = False

                # 如果启用自动下载，尝试下载新版本
                if self.auto_download:
                    download_success = self.download_tsp_file(script_id, remote_version)
                else:
                    script_display_name = self.get_script_display_name(script_id)
                    self.logger.info(f"📋 {script_display_name} 有新版本 {remote_version}，但自动下载已禁用")

                update_info = {
                    "script_id": script_id,
                    "script_name": self.script_mapping.get(script_id, {}).get('name', script_id),
                    "folder_name": self.get_script_folder(script_id),
                    "local_version": local_version,
                    "remote_version": remote_version,
                    "download_success": download_success,
                    "auto_download_enabled": self.auto_download,
                    "timestamp": datetime.now().isoformat()
                }
                updates.append(update_info)
            
            # 避免请求过于频繁
            time.sleep(1)
        
        return updates
    
    def send_alert(self, updates: List[Dict]):
        """
        发送更新警报
        
        Args:
            updates: 更新信息列表
        """
        if not updates:
            return
        
        alert_message = f"🚨 TSP版本更新警报 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        for update in updates:
            download_status = "✅ 下载成功" if update.get('download_success', False) else "❌ 下载失败"
            alert_message += (
                f"脚本名称: {update['script_name']}\n"
                f"脚本ID: {update['script_id']}\n"
                f"文件夹: {update['folder_name']}\n"
                f"本地版本: {update['local_version']}\n"
                f"云端版本: {update['remote_version']}\n"
                f"下载状态: {download_status}\n"
                f"检测时间: {update['timestamp']}\n"
                f"{'='*50}\n"
            )
        
        # 记录到日志
        self.logger.critical(f"发现 {len(updates)} 个脚本有版本更新!")
        self.logger.critical(alert_message)
        
        # 这里可以添加其他警报方式，如邮件、微信、钉钉等
        # 例如：self.send_email_alert(alert_message)
        # 例如：self.send_wechat_alert(alert_message)
    
    def run_once(self) -> bool:
        """
        执行一次检查
        
        Returns:
            是否发现更新
        """
        try:
            updates = self.check_all_scripts()
            
            if updates:
                self.send_alert(updates)
                return True
            else:
                self.logger.info("所有脚本版本都是最新的")
                return False
                
        except Exception as e:
            self.logger.error(f"执行检查时发生错误: {e}")
            return False
    
    def run_continuous(self, check_interval: int = 300, max_checks: int = 0):
        """
        循环运行监控（在脚本运行期间）

        Args:
            check_interval: 检查间隔（秒），默认5分钟
            max_checks: 最大检查次数，0表示无限制
        """
        self.logger.info(f"开始循环监控，检查间隔: {check_interval}秒")
        if max_checks > 0:
            self.logger.info(f"最大检查次数: {max_checks}")
        else:
            self.logger.info("无限制检查次数，按Ctrl+C停止")

        check_count = 0
        try:
            while True:
                check_count += 1
                self.logger.info("="*60)
                self.logger.info(f"第 {check_count} 次检查 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

                self.run_once()

                # 检查是否达到最大检查次数
                if max_checks > 0 and check_count >= max_checks:
                    self.logger.info(f"已完成 {max_checks} 次检查，监控结束")
                    break

                self.logger.info(f"本轮检查完成，等待 {check_interval} 秒后进行下一轮检查...")
                self.logger.info("按 Ctrl+C 可随时停止监控")
                time.sleep(check_interval)

        except KeyboardInterrupt:
            self.logger.info(f"\n收到中断信号，已完成 {check_count} 次检查，停止监控")
        except Exception as e:
            self.logger.error(f"监控过程中发生严重错误: {e}")
            raise


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='TSP版本监控工具')
    parser.add_argument('--once', action='store_true', help='只执行一次检查')
    parser.add_argument('--interval', type=int, default=300, help='检查间隔（秒），默认300秒（5分钟）')
    parser.add_argument('--max-checks', type=int, default=0, help='最大检查次数，0表示无限制')
    parser.add_argument('--config', type=str, default='config.json', help='配置文件路径')
    parser.add_argument('--no-download', action='store_true', help='禁用自动下载新版本')

    args = parser.parse_args()

    monitor = TSPVersionMonitor(args.config, auto_download=not args.no_download)

    if args.once:
        # 单次运行模式
        print("执行单次检查...")
        monitor.run_once()
    else:
        # 循环运行模式
        print(f"开始循环监控，检查间隔: {args.interval}秒")
        if args.max_checks > 0:
            print(f"最大检查次数: {args.max_checks}")
        print("按 Ctrl+C 停止监控")
        monitor.run_continuous(args.interval, args.max_checks)


if __name__ == "__main__":
    main()
