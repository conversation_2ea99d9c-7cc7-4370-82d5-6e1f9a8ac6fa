#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TSP版本监控工具 - macOS快速启动脚本
"""

import sys
import os
from tsp_version_monitor import TSPVersionMonitor


def print_banner():
    """打印欢迎横幅"""
    print("=" * 60)
    print("🚀 TSP版本监控工具 - macOS版")
    print("=" * 60)
    print()


def print_menu():
    """打印菜单"""
    print("请选择运行模式:")
    print("1. 🔍 单次检查 - 立即检查所有脚本版本")
    print("2. 🔄 循环监控 - 每5分钟检查一次")
    print("3. ⚡ 快速监控 - 每1分钟检查一次")
    print("4. 🎯 自定义监控 - 自定义检查间隔和次数")
    print("5. 📋 查看配置 - 显示当前监控的脚本")
    print("6. 🧪 测试连接 - 测试API连接")
    print("7. ⚙️  设置选项 - 配置自动下载等选项")
    print("0. 🚪 退出")
    print()


def show_config(monitor):
    """显示当前配置"""
    print("📋 当前监控配置:")
    print("-" * 60)
    for script_id, version in monitor.script_versions.items():
        script_name = monitor.script_mapping.get(script_id, {}).get('name', script_id)
        folder_name = monitor.get_script_folder(script_id)
        print(f"脚本名称: {script_name:<12} | ID: {script_id} | 版本: {version} | 文件夹: {folder_name}")
    print("-" * 60)
    print(f"总共监控 {len(monitor.script_versions)} 个脚本")
    print()


def test_connection(monitor):
    """测试API连接"""
    print("🧪 测试API连接...")
    print("-" * 40)
    
    # 选择第一个脚本进行测试
    if monitor.script_versions:
        script_id = list(monitor.script_versions.keys())[0]
        version = monitor.script_versions[script_id]
        script_display_name = monitor.get_script_display_name(script_id)

        print(f"使用脚本 {script_display_name} (版本 {version}) 进行测试...")

        try:
            has_update, remote_version = monitor.check_single_script(script_id, version)
            print("✅ API连接正常")
            print(f"远程版本: {remote_version}")
            if has_update:
                print("🚨 发现版本更新!")
            else:
                print("✅ 版本正常")
        except Exception as e:
            print(f"❌ 连接测试失败: {e}")
    else:
        print("❌ 没有配置的脚本")
    
    print("-" * 40)
    print()


def get_positive_int(prompt, default=None):
    """获取正整数输入"""
    while True:
        try:
            value = input(prompt)
            if not value and default is not None:
                return default
            value = int(value)
            if value > 0:
                return value
            else:
                print("请输入大于0的数字")
        except ValueError:
            print("请输入有效的数字")


def custom_monitor(monitor):
    """自定义监控设置"""
    print("🎯 自定义监控设置")
    print("-" * 40)
    
    # 获取检查间隔
    print("设置检查间隔:")
    print("  60  = 1分钟")
    print("  300 = 5分钟")
    print("  600 = 10分钟")
    print("  1800 = 30分钟")
    
    interval = get_positive_int("检查间隔（秒，默认300）: ", 300)
    
    # 获取最大检查次数
    print("\n设置检查次数:")
    print("  0 = 无限制（按Ctrl+C停止）")
    print("  12 = 检查12次")
    print("  24 = 检查24次")
    
    max_checks = int(input("最大检查次数（默认0，无限制）: ") or "0")
    
    print(f"\n🔄 开始自定义监控:")
    print(f"   检查间隔: {interval}秒 ({interval//60}分钟)")
    if max_checks > 0:
        print(f"   检查次数: {max_checks}次")
        total_time = interval * max_checks
        print(f"   预计运行时间: {total_time//60}分钟")
    else:
        print(f"   检查次数: 无限制")
    print(f"   按 Ctrl+C 可随时停止")
    
    input("\n按回车键开始监控...")
    monitor.run_continuous(interval, max_checks)


def configure_settings(monitor):
    """配置设置"""
    print("⚙️  当前设置:")
    print("-" * 40)
    print(f"自动下载: {'✅ 启用' if monitor.auto_download else '❌ 禁用'}")
    print(f"配置文件: {monitor.config_file}")
    print(f"映射文件: {monitor.mapping_file}")
    print("-" * 40)

    print("\n设置选项:")
    print("1. 切换自动下载状态")
    print("2. 返回主菜单")

    choice = input("请选择 (1-2): ").strip()

    if choice == "1":
        monitor.auto_download = not monitor.auto_download
        status = "启用" if monitor.auto_download else "禁用"
        print(f"✅ 自动下载已{status}")

    input("\n按回车键继续...")


def main():
    """主函数"""
    print_banner()

    # 检查配置文件
    if not os.path.exists("config.json"):
        print("❌ 未找到配置文件 config.json")
        print("💡 请确保配置文件存在并包含正确的脚本ID和版本号")
        return

    try:
        # 创建监控器
        monitor = TSPVersionMonitor()
        
        while True:
            print_menu()
            choice = input("请输入选择 (0-7): ").strip()
            
            if choice == "0":
                print("👋 再见!")
                break
            elif choice == "1":
                print("🔍 执行单次检查...")
                print("-" * 40)
                monitor.run_once()
                print("-" * 40)
                input("按回车键继续...")
                
            elif choice == "2":
                print("🔄 开始循环监控（每5分钟检查一次）")
                print("按 Ctrl+C 停止监控")
                print("-" * 40)
                monitor.run_continuous(300)  # 5分钟
                
            elif choice == "3":
                print("⚡ 开始快速监控（每1分钟检查一次）")
                print("按 Ctrl+C 停止监控")
                print("-" * 40)
                monitor.run_continuous(60)  # 1分钟
                
            elif choice == "4":
                custom_monitor(monitor)
                
            elif choice == "5":
                show_config(monitor)
                input("按回车键继续...")
                
            elif choice == "6":
                test_connection(monitor)
                input("按回车键继续...")

            elif choice == "7":
                configure_settings(monitor)

            else:
                print("❌ 无效选择，请重新输入")
            
            print()
    
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断，再见!")
    except Exception as e:
        print(f"❌ 程序运行出错: {e}")
        print("💡 请检查网络连接和配置文件")


if __name__ == "__main__":
    main()
