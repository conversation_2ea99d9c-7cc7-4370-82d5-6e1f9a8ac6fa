# 添加新脚本ID指南

本文档详细说明如何在TSP版本监控工具中添加新的script_id。

## 📋 需要修改的文件

当您需要添加新的script_id时，需要修改以下文件：

### 1. config.json（必须）
这是主要的配置文件，包含脚本ID和版本号的映射。

**位置**: 项目根目录下的 `config.json`

**修改方法**:
```json
{
  "33085": "1.0.81",
  "34301": "1.0.17",
  "新的脚本ID": "初始版本号"
}
```

**示例**:
假设要添加脚本ID `12345`，初始版本为 `1.0.0`：
```json
{
  "33085": "1.0.81",
  "34301": "1.0.17",
  "34422": "1.0.19",
  "33586": "1.0.29",
  "33081": "1.0.224",
  "33086": "1.0.39",
  "33084": "1.0.45",
  "33609": "1.0.74",
  "12345": "1.0.0"
}
```

### 2. script_mapping.json（推荐）
这个文件包含脚本的详细信息，包括名称和文件夹映射。

**位置**: 项目根目录下的 `script_mapping.json`

**修改方法**:
```json
{
  "现有脚本ID": {
    "name": "脚本名称",
    "version": "版本号",
    "folder": "文件夹名称"
  },
  "新的脚本ID": {
    "name": "新脚本的名称",
    "version": "初始版本号",
    "folder": "新脚本的文件夹名称"
  }
}
```

**示例**:
添加脚本ID `12345`，名称为 `测试脚本`：
```json
{
  "33085": {
    "name": "补",
    "version": "1.0.81",
    "folder": "补"
  },
  "12345": {
    "name": "测试脚本",
    "version": "1.0.0",
    "folder": "测试脚本"
  }
}
```

## 📁 创建文件夹

### 3. 创建对应的文件夹（推荐）
为新脚本创建专用文件夹来存放相关文件。

**方法一：手动创建**
```bash
mkdir "新脚本的文件夹名称"
```

**方法二：使用脚本创建**
修改完配置文件后，运行：
```bash
python3 setup_folders.py
```
这会自动为所有脚本创建文件夹和README文件。

## 📁 文件夹结构说明

每个脚本的文件夹结构如下：
```
脚本文件夹名称/
├── README.md           # 脚本说明文件
├── 1.0.0/             # 版本1.0.0的文件夹
│   └── 脚本ID_v1.0.0_时间戳.tsp
├── 1.0.1/             # 版本1.0.1的文件夹
│   └── 脚本ID_v1.0.1_时间戳.tsp
└── ...                # 其他版本文件夹
```

**说明**：
- 每个新版本会创建独立的子文件夹
- 子文件夹名称为版本号（如 `1.0.0`、`1.0.1`）
- TSP文件保存在对应版本的文件夹中
- 文件命名格式：`脚本ID_v版本号_时间戳.tsp`

## 🔄 完整添加流程

### 步骤1：确定脚本信息
- 脚本ID（必须）
- 脚本名称（推荐）
- 初始版本号（必须）
- 文件夹名称（推荐）

### 步骤2：修改配置文件
1. 编辑 `config.json`，添加新的脚本ID和版本号
2. 编辑 `script_mapping.json`，添加脚本的详细信息

### 步骤3：创建文件夹
```bash
# 方法1：手动创建
mkdir "新脚本文件夹名称"

# 方法2：使用脚本自动创建
python3 setup_folders.py
```

### 步骤4：验证配置
```bash
# 测试配置是否正确
python3 -c "from tsp_version_monitor import TSPVersionMonitor; m = TSPVersionMonitor(); print('配置正确')"

# 或者使用快速启动脚本查看配置
python3 quick_start.py
# 选择 "5. 📋 查看配置"
```

### 步骤5：测试监控
```bash
# 执行单次检查，验证新脚本是否被正确监控
python3 tsp_version_monitor.py --once
```

## 📝 完整示例

假设要添加一个新脚本：
- **脚本ID**: `99999`
- **脚本名称**: `新功能脚本`
- **初始版本**: `1.0.0`
- **文件夹名称**: `新功能脚本`

### 1. 修改 config.json
```json
{
  "33085": "1.0.81",
  "34301": "1.0.17",
  "34422": "1.0.19",
  "33586": "1.0.29",
  "33081": "1.0.224",
  "33086": "1.0.39",
  "33084": "1.0.45",
  "33609": "1.0.74",
  "99999": "1.0.0"
}
```

### 2. 修改 script_mapping.json
```json
{
  "33085": {
    "name": "补",
    "version": "1.0.81",
    "folder": "补"
  },
  "34301": {
    "name": "zzzzzz",
    "version": "1.0.17",
    "folder": "zzzzzz"
  },
  "34422": {
    "name": "畅",
    "version": "1.0.19",
    "folder": "畅"
  },
  "33586": {
    "name": "sndgbxzcvasgf",
    "version": "1.0.29",
    "folder": "sndgbxzcvasgf"
  },
  "33081": {
    "name": "kpvskjsev",
    "version": "1.0.224",
    "folder": "kpvskjsev"
  },
  "33086": {
    "name": "挖",
    "version": "1.0.39",
    "folder": "挖"
  },
  "33084": {
    "name": "商青",
    "version": "1.0.45",
    "folder": "商青"
  },
  "33609": {
    "name": "zzzzqtsa",
    "version": "1.0.74",
    "folder": "zzzzqtsa"
  },
  "99999": {
    "name": "新功能脚本",
    "version": "1.0.0",
    "folder": "新功能脚本"
  }
}
```

### 3. 创建文件夹
```bash
mkdir "新功能脚本"
# 或者运行
python3 setup_folders.py
```

### 4. 验证
```bash
python3 quick_start.py
# 选择 "5. 📋 查看配置" 查看是否包含新脚本
```

## ⚠️ 注意事项

1. **JSON格式**: 确保JSON文件格式正确，注意逗号和引号
2. **版本号格式**: 使用标准的版本号格式，如 `1.0.0`、`2.1.5` 等
3. **文件夹名称**: 避免使用特殊字符，建议使用中文、英文、数字
4. **备份**: 修改配置文件前建议先备份
5. **测试**: 添加新脚本后建议先进行单次检查测试

## 🔧 故障排除

### 配置文件格式错误
**症状**: 程序启动失败，提示JSON解析错误
**解决**: 使用JSON验证工具检查文件格式，确保语法正确

### 脚本ID不存在
**症状**: 监控时显示API错误或找不到脚本
**解决**: 确认脚本ID是否正确，是否在TouchSprite平台上存在

### 文件夹创建失败
**症状**: 下载文件时提示无法创建文件夹
**解决**: 检查文件夹名称是否包含非法字符，确保有写入权限

## 📞 获取帮助

如果在添加新脚本时遇到问题，可以：
1. 检查日志文件：`logs/tsp_monitor_YYYYMMDD.log`
2. 运行测试：`python3 test_monitor.py`
3. 使用调试模式：`python3 tsp_version_monitor.py --once`
