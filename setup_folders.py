#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为每个脚本文件夹创建README文件
"""

import json
import os


def create_folder_readme(script_id, script_info):
    """为指定脚本创建README文件"""
    folder_name = script_info['folder']
    script_name = script_info['name']
    version = script_info['version']
    
    readme_content = f"""# {script_name} 脚本文件夹

## 脚本信息
- **脚本名称**: {script_name}
- **脚本ID**: {script_id}
- **当前版本**: {version}
- **文件夹名称**: {folder_name}

## 文件夹用途
此文件夹用于存放脚本 "{script_name}" (ID: {script_id}) 相关的文件和资源。

## 文件夹结构
```
{folder_name}/
├── README.md           # 本说明文件
├── 1.0.0/             # 版本1.0.0的文件夹
│   └── {script_id}_v1.0.0_时间戳.tsp
├── 1.0.1/             # 版本1.0.1的文件夹
│   └── {script_id}_v1.0.1_时间戳.tsp
└── ...                # 其他版本文件夹
```

## 可能包含的文件类型
- **版本文件夹**: 以版本号命名的子文件夹（如 `1.0.0`、`1.0.1`）
- **TSP脚本文件**: 下载的脚本文件 (.tsp)
- **配置文件**: 脚本相关的配置文件
- **资源文件**: 图片、音频等资源文件
- **日志文件**: 脚本运行日志
- **说明文档**: 版本更新说明等

## 版本管理
- 每个新版本都会创建独立的子文件夹
- 文件夹名称为版本号（如 `{version}`）
- 下载的TSP文件保存在对应版本文件夹中
- 文件命名格式：`{script_id}_v{version}_时间戳.tsp`

## 版本更新记录
当检测到此脚本有新版本时：
1. 自动下载新版本TSP文件
2. 创建以新版本号命名的文件夹
3. 将文件保存到版本文件夹中
4. 更新本地版本记录
5. 记录详细日志信息

---
*此README由TSP版本监控工具自动生成*
"""
    
    readme_path = os.path.join(folder_name, "README.md")
    
    try:
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print(f"✅ 已创建 {folder_name}/README.md")
    except Exception as e:
        print(f"❌ 创建 {folder_name}/README.md 失败: {e}")


def main():
    """主函数"""
    print("🚀 为脚本文件夹创建README文件...")
    print("-" * 50)
    
    # 读取脚本映射文件
    try:
        with open('script_mapping.json', 'r', encoding='utf-8') as f:
            script_mapping = json.load(f)
    except Exception as e:
        print(f"❌ 读取script_mapping.json失败: {e}")
        return
    
    # 为每个脚本创建README
    for script_id, script_info in script_mapping.items():
        create_folder_readme(script_id, script_info)
    
    print("-" * 50)
    print(f"✅ 完成！已为 {len(script_mapping)} 个脚本文件夹创建README文件")


if __name__ == "__main__":
    main()
