#!/bin/bash

# TSP版本监控启动脚本 (macOS版本)

echo "🚀 启动TSP版本监控工具..."

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到Python3，请先安装Python3"
    echo "💡 建议: brew install python3"
    exit 1
fi

# 检查依赖
echo "📦 检查依赖..."
pip3 install -r requirements.txt

# 创建日志目录
mkdir -p logs

echo ""
echo "请选择运行模式:"
echo "1. 前台运行（推荐，可以看到实时输出）"
echo "2. 后台运行"
echo "3. 单次检查"
echo "4. 自定义参数运行"

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo "🔄 前台运行模式，按 Ctrl+C 停止..."
        python3 tsp_version_monitor.py
        ;;
    2)
        echo "🔄 后台运行模式..."
        nohup python3 tsp_version_monitor.py > logs/monitor_output.log 2>&1 &
        PID=$!
        echo "✅ 监控程序已启动，进程ID: $PID"
        echo $PID > monitor.pid
        echo "📋 查看日志: tail -f logs/tsp_monitor_$(date +%Y%m%d).log"
        echo "🛑 停止监控: ./stop_monitor.sh"
        ;;
    3)
        echo "🔍 执行单次检查..."
        python3 tsp_version_monitor.py --once
        ;;
    4)
        read -p "检查间隔（秒，默认300）: " interval
        read -p "最大检查次数（0表示无限制，默认0）: " max_checks

        interval=${interval:-300}
        max_checks=${max_checks:-0}

        echo "🔄 自定义参数运行: 间隔${interval}秒, 最大${max_checks}次"
        python3 tsp_version_monitor.py --interval $interval --max-checks $max_checks
        ;;
    *)
        echo "❌ 无效选择，退出"
        exit 1
        ;;
esac
