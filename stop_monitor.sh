#!/bin/bash

# TSP版本监控停止脚本

echo "停止TSP版本监控工具..."

# 检查PID文件是否存在
if [ -f "monitor.pid" ]; then
    PID=$(cat monitor.pid)
    
    # 检查进程是否还在运行
    if ps -p $PID > /dev/null 2>&1; then
        echo "正在停止进程 $PID..."
        kill $PID
        
        # 等待进程结束
        sleep 2
        
        # 强制杀死进程（如果还在运行）
        if ps -p $PID > /dev/null 2>&1; then
            echo "强制停止进程 $PID..."
            kill -9 $PID
        fi
        
        echo "监控程序已停止"
    else
        echo "进程 $PID 已经停止"
    fi
    
    # 删除PID文件
    rm -f monitor.pid
else
    echo "未找到PID文件，尝试查找并停止相关进程..."
    
    # 查找并停止相关进程
    PIDS=$(pgrep -f "tsp_version_monitor.py")
    if [ -n "$PIDS" ]; then
        echo "找到相关进程: $PIDS"
        kill $PIDS
        echo "已停止相关进程"
    else
        echo "未找到运行中的监控进程"
    fi
fi

echo "停止完成"
