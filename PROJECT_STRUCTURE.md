# TSP版本监控工程项目结构 (macOS版)

```
get_tsp_update/
├── tsp_version_monitor.py      # 主程序文件
├── config.json                 # 配置文件（脚本ID和版本号映射）
├── script_mapping.json         # 脚本映射文件（ID到名称和文件夹的映射）
├── requirements.txt            # Python依赖包列表
├── quick_start.py              # 快速启动脚本（推荐使用）
├── example_usage.py            # 使用示例和演示代码
├── test_monitor.py             # 单元测试和集成测试
├── setup_folders.py            # 文件夹设置脚本
├── start_monitor.sh            # 启动脚本（macOS优化）
├── stop_monitor.sh             # 停止脚本（macOS）
├── README.md                   # 项目说明文档
├── USAGE_GUIDE.md              # 详细使用指南
├── ADD_NEW_SCRIPT_GUIDE.md     # 添加新脚本ID指南
├── PROJECT_STRUCTURE.md        # 项目结构说明（本文件）
├── logs/                       # 日志目录（运行时自动创建）
│   ├── tsp_monitor_YYYYMMDD.log    # 每日日志文件
│   └── monitor_output.log          # 后台运行输出日志
├── 补/                         # 脚本"补"(ID:33085)的文件夹
│   ├── README.md               # 脚本说明文件
│   ├── 1.0.81/                 # 版本1.0.81文件夹
│   ├── 1.0.82/                 # 版本1.0.82文件夹（如有更新）
│   └── ...                     # 其他版本文件夹
├── zzzzzz/                     # 脚本"zzzzzz"(ID:34301)的文件夹
│   ├── README.md
│   ├── 1.0.17/                 # 版本文件夹
│   └── ...
├── 畅/                         # 脚本"畅"(ID:34422)的文件夹
│   ├── README.md
│   ├── 1.0.19/                 # 版本文件夹
│   └── ...
├── sndgbxzcvasgf/              # 脚本"sndgbxzcvasgf"(ID:33586)的文件夹
│   ├── README.md
│   ├── 1.0.29/                 # 版本文件夹
│   └── ...
├── kpvskjsev/                  # 脚本"kpvskjsev"(ID:33081)的文件夹
│   ├── README.md
│   ├── 1.0.224/                # 版本文件夹
│   └── ...
├── 挖/                         # 脚本"挖"(ID:33086)的文件夹
│   ├── README.md
│   ├── 1.0.39/                 # 版本文件夹
│   └── ...
├── 商青/                       # 脚本"商青"(ID:33084)的文件夹
│   ├── README.md
│   ├── 1.0.45/                 # 版本文件夹
│   └── ...
└── zzzzqtsa/                   # 脚本"zzzzqtsa"(ID:33609)的文件夹
    ├── README.md
    ├── 1.0.74/                 # 版本文件夹
    └── ...                     # 其他版本文件夹
```

## 文件说明

### 核心文件

- **tsp_version_monitor.py**: 主程序，包含TSPVersionMonitor类和所有核心功能
- **config.json**: 配置文件，定义需要监控的脚本ID和对应的本地版本号
- **script_mapping.json**: 脚本映射文件，定义脚本ID到名称和文件夹的映射关系
- **requirements.txt**: Python依赖包，目前只需要requests库

### 辅助工具

- **quick_start.py**: 快速启动脚本，提供友好的交互式界面（推荐使用）
- **example_usage.py**: 提供多种使用示例，帮助理解和测试功能
- **test_monitor.py**: 完整的单元测试套件，确保代码质量
- **setup_folders.py**: 文件夹设置脚本，为每个脚本创建对应文件夹和README
- **start_monitor.sh**: 便捷的启动脚本，支持多种运行模式
- **stop_monitor.sh**: 停止监控程序的脚本

### 脚本文件夹

每个监控的脚本都有对应的文件夹，用于存放相关文件：
- **补/**: 脚本"补"(ID:33085)的专用文件夹
- **zzzzzz/**: 脚本"zzzzzz"(ID:34301)的专用文件夹
- **畅/**: 脚本"畅"(ID:34422)的专用文件夹
- **sndgbxzcvasgf/**: 脚本"sndgbxzcvasgf"(ID:33586)的专用文件夹
- **kpvskjsev/**: 脚本"kpvskjsev"(ID:33081)的专用文件夹
- **挖/**: 脚本"挖"(ID:33086)的专用文件夹
- **商青/**: 脚本"商青"(ID:33084)的专用文件夹
- **zzzzqtsa/**: 脚本"zzzzqtsa"(ID:33609)的专用文件夹

### 文档

- **README.md**: 详细的使用说明和配置指南
- **USAGE_GUIDE.md**: 详细的使用指南，包含故障排除和技巧

### 运行时文件

- **logs/**: 日志目录，包含详细的运行日志和错误信息
- **monitor.pid**: 进程ID文件（运行时生成）
- **__pycache__/**: Python字节码缓存目录

## 快速开始

### 方法一：使用快速启动脚本（推荐）
```bash
python3 quick_start.py
```
提供友好的交互式界面，适合macOS用户。

### 方法二：使用Shell脚本
```bash
./start_monitor.sh
```
提供多种运行模式选择。

### 方法三：直接命令行
1. **安装依赖**:
   ```bash
   pip3 install -r requirements.txt
   ```

2. **配置脚本**:
   编辑 `config.json` 文件，设置需要监控的脚本ID和版本号

3. **运行测试**:
   ```bash
   python3 test_monitor.py
   ```

4. **单次检查**:
   ```bash
   python3 tsp_version_monitor.py --once
   ```

5. **循环监控**:
   ```bash
   python3 tsp_version_monitor.py --interval 300
   ```

## 功能特性

- ✅ 循环监控（运行期间）
- ✅ 智能版本号比较
- ✅ 自动下载新版本TSP文件
- ✅ 脚本专用文件夹管理
- ✅ 友好的脚本名称显示
- ✅ 详细日志记录
- ✅ 异常处理和恢复
- ✅ 配置文件管理
- ✅ 单元测试覆盖
- ✅ macOS优化界面
- ✅ 使用示例和文档

## 扩展性

代码设计具有良好的扩展性，可以轻松添加：
- 更多警报方式（邮件、微信、钉钉等）
- 不同的版本比较策略
- 更复杂的配置管理
- 数据库存储支持
- Web界面管理
- API接口服务
