# TSP版本监控工具

一个用于检测多个云端TSP文件版本更新的Python工具，专为macOS环境优化。

## 功能特性

- 🔄 循环监控多个TSP脚本版本（运行期间）
- 📊 智能版本号比较算法
- 🚨 版本更新自动警报
- 📥 自动下载新版本TSP文件
- 📁 为每个脚本创建专用文件夹
- 📝 详细的日志记录
- ⚙️ 灵活的配置管理
- 🛡️ 完善的异常处理
- 🖥️ macOS友好的用户界面
- ⌨️ 支持Ctrl+C优雅停止

## 环境要求

- macOS 10.14+
- Python 3.7+
- 网络连接

## 安装依赖

```bash
# 使用pip安装
pip3 install -r requirements.txt

# 或者使用brew安装Python（如果还没有）
brew install python3
pip3 install requests
```

## 配置说明

编辑 `config.json` 文件来配置需要监控的脚本ID和对应的本地版本号：

```json
{
  "33085": "1.0.81",
  "34301": "1.0.17",
  "34422": "1.0.19",
  "33586": "1.0.29",
  "33081": "1.0.224",
  "33086": "1.0.39",
  "33084": "1.0.45",
  "33609": "1.0.74"
}
```

## 使用方法

### 快速启动（推荐）
```bash
./start_monitor.sh
```
脚本会提供交互式菜单，让您选择运行模式。

### 命令行使用

#### 循环监控模式（默认，5分钟间隔）
```bash
python3 tsp_version_monitor.py
```

#### 单次检查模式
```bash
python3 tsp_version_monitor.py --once
```

#### 自定义间隔（例如每2分钟检查一次）
```bash
python3 tsp_version_monitor.py --interval 120
```

#### 限制检查次数（例如只检查10次）
```bash
python3 tsp_version_monitor.py --max-checks 10
```

#### 使用自定义配置文件
```bash
python3 tsp_version_monitor.py --config my_config.json
```

#### 禁用自动下载（仅检查版本，不下载文件）
```bash
python3 tsp_version_monitor.py --no-download
```

## API接口说明

工具通过POST请求访问以下接口：
- **URL**: `http://app.touchsprite.com/api/device`
- **请求头**: 
  - `Content-Type: application/x-www-form-urlencoded`
  - `resource: android`
- **请求体参数**:
  - `uuid`: 固定值 `0198a5cf9a4c65cf4fc54ba4b58224be`
  - `script_id`: 脚本ID（从配置文件读取）
  - `device_os`: 固定值 `android`
  - `version`: 本地版本号（从配置文件读取）

## 日志文件

日志文件保存在 `logs/` 目录下，按日期命名：
- 格式：`tsp_monitor_YYYYMMDD.log`
- 包含详细的检查记录、错误信息和警报信息

## 版本比较逻辑

工具使用智能版本号比较算法：
1. 将版本号按 `.` 分割为数字数组
2. 逐位比较各个版本号段
3. 当云端版本号高于本地版本号时触发警报

## 扩展功能

可以在 `send_alert()` 方法中添加更多警报方式：
- 邮件通知
- 微信/钉钉机器人
- 短信通知
- Webhook回调

## 注意事项

1. 确保网络连接正常
2. API接口可能有频率限制，工具已内置1秒延迟
3. 建议在服务器上使用 `nohup` 或 `systemd` 运行
4. 定期检查日志文件，避免磁盘空间不足

## 故障排除

1. **网络连接问题**: 检查网络连接和防火墙设置
2. **API响应异常**: 查看日志文件中的详细错误信息
3. **配置文件错误**: 确保 `config.json` 格式正确
4. **权限问题**: 确保程序有写入日志文件的权限
