# 🚀 TSP版本监控工具运行指南

## 📋 运行前准备

### 1. 确认环境
- ✅ macOS系统
- ✅ Python 3.7+
- ✅ 网络连接正常

### 2. 安装依赖
```bash
pip3 install -r requirements.txt
```

### 3. 确认配置文件
确保以下文件存在且配置正确：
- `config.json` - 脚本ID和版本号配置
- `script_mapping.json` - 脚本映射信息

## 🎯 运行方式

### 方式一：快速启动脚本（推荐）

**命令**：
```bash
python3 quick_start.py
```

**特点**：
- 🖥️ 友好的交互界面
- 📋 清晰的选项菜单
- ⚙️ 可配置各种选项
- 🔍 实时查看配置和状态

**菜单选项说明**：
1. **🔍 单次检查** - 立即检查所有脚本版本，适合快速了解当前状态
2. **🔄 循环监控** - 每5分钟检查一次，适合日常监控
3. **⚡ 快速监控** - 每1分钟检查一次，适合密集监控
4. **🎯 自定义监控** - 自定义检查间隔和次数
5. **📋 查看配置** - 显示当前监控的所有脚本信息
6. **🧪 测试连接** - 测试API连接是否正常
7. **⚙️ 设置选项** - 配置自动下载等选项

### 方式二：命令行直接运行

#### 单次检查
```bash
python3 tsp_version_monitor.py --once
```

#### 循环监控（默认5分钟间隔）
```bash
python3 tsp_version_monitor.py
```

#### 自定义间隔（例如每2分钟检查一次）
```bash
python3 tsp_version_monitor.py --interval 120
```

#### 限制检查次数（例如只检查10次）
```bash
python3 tsp_version_monitor.py --max-checks 10
```

#### 禁用自动下载（只检查版本，不下载文件）
```bash
python3 tsp_version_monitor.py --no-download
```

### 方式三：Shell脚本启动

```bash
./start_monitor.sh
```

提供多种运行模式选择，包括前台运行、后台运行等。

## 📊 运行示例

### 示例1：首次运行建议
```bash
# 1. 先查看配置
python3 quick_start.py
# 选择 "5. 📋 查看配置"

# 2. 测试连接
# 选择 "6. 🧪 测试连接"

# 3. 执行单次检查
# 选择 "1. 🔍 单次检查"
```

### 示例2：日常监控
```bash
# 启动循环监控
python3 quick_start.py
# 选择 "2. 🔄 循环监控"
# 按 Ctrl+C 停止
```

### 示例3：自定义监控
```bash
# 每30秒检查一次，总共检查20次
python3 tsp_version_monitor.py --interval 30 --max-checks 20
```

## 📝 运行输出说明

### 正常输出示例
```
✅ 脚本 补 (33085) 版本正常: 1.0.81
✅ 脚本 zzzzzz (34301) 版本正常: 1.0.17
```

### 发现更新示例
```
🚨 脚本 补 (33085) 发现更新: 1.0.81 -> 1.0.82
🔄 开始下载 补 (33085) 的新版本 1.0.82...
📁 创建版本文件夹: 补/1.0.82
✅ 下载成功: 补/1.0.82/33085_v1.0.82_20251031_173000.tsp (1024 字节)
📝 已更新 补 (33085) 的本地版本号为: 1.0.82
```

### 错误输出示例
```
❌ 请求失败 - 脚本 补 (33085): HTTP 500
❌ 请求超时 - 脚本 zzzzzz (34301)
```

## 📁 文件组织

运行后会自动创建以下结构：
```
项目目录/
├── logs/                       # 日志文件
│   └── tsp_monitor_20251031.log
├── 补/                         # 脚本文件夹
│   ├── README.md
│   ├── 1.0.81/                 # 版本文件夹
│   └── 1.0.82/                 # 新版本文件夹
└── ...                         # 其他脚本文件夹
```

## 🛑 停止监控

### 前台运行时
按 `Ctrl + C` 即可优雅停止

### 后台运行时
```bash
./stop_monitor.sh
```

## 📋 日志查看

### 查看今天的日志
```bash
tail -f logs/tsp_monitor_$(date +%Y%m%d).log
```

### 查看最新日志
```bash
ls -la logs/
tail -f logs/tsp_monitor_最新日期.log
```

## 🔧 常见问题

### Q: 程序启动失败
**A**: 检查Python环境和依赖包
```bash
python3 --version
pip3 install -r requirements.txt
```

### Q: 网络连接错误
**A**: 检查网络连接和API地址
```bash
curl -I http://app.touchsprite.com/api/device
```

### Q: 配置文件错误
**A**: 验证JSON格式
```bash
python3 -c "import json; print(json.load(open('config.json')))"
```

### Q: 权限问题
**A**: 确保有写入权限
```bash
chmod +x start_monitor.sh stop_monitor.sh
mkdir -p logs
```

## 💡 使用建议

1. **首次使用**：建议先执行单次检查，确认配置正确
2. **日常监控**：使用5分钟间隔的循环监控
3. **重要更新期**：使用1分钟间隔的快速监控
4. **测试阶段**：可以禁用自动下载，只检查版本
5. **长期运行**：建议定期查看日志文件

## 📞 获取帮助

```bash
# 查看命令行帮助
python3 tsp_version_monitor.py --help

# 运行测试
python3 test_monitor.py

# 查看项目结构
cat PROJECT_STRUCTURE.md
```
